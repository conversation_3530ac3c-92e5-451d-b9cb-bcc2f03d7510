<script setup lang="ts">
import Logo from '@/components/ui/Logo.vue'
</script>

<template>
  <div class="min-h-screen bg-base-100">
    <!-- Navigation -->
    <nav class="bg-base-100 shadow-sm border-b border-base-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <Logo />
          </div>
          <div class="flex items-center space-x-4">
            <a href="/docs" class="text-base-content/70 hover:text-base-content text-sm">
              API docs
            </a>
            <router-link to="/register" class="bg-primary hover:bg-primary/90 text-primary-content px-4 py-2 rounded-md text-sm font-medium">
              Get started
            </router-link>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-base-100 via-base-200 to-primary/5 border-t border-base-300/50 mt-auto">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="col-span-1 md:col-span-2">
            <Logo size="sm" text-class="text-lg" />
            <p class="mt-4 text-sm text-base-content/80">
              GDPR-compliant email-to-webhook service for European businesses.
            </p>
            <div class="mt-4 flex items-center space-x-2">
              <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
              <span class="text-xs text-base-content/60">EU hosted & operated</span>
            </div>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-base-content tracking-wider uppercase mb-4">Product</h3>
            <ul class="space-y-3">
              <li>
                <a href="/#features" class="text-sm text-base-content/70 hover:text-primary transition-colors">Features</a>
              </li>
              <li>
                <a href="/#pricing" class="text-sm text-base-content/70 hover:text-primary transition-colors">Pricing</a>
              </li>
              <li>
                <a href="/docs" class="text-sm text-base-content/70 hover:text-primary transition-colors">Documentation</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 class="text-sm font-semibold text-base-content tracking-wider uppercase mb-4">Support</h3>
            <ul class="space-y-3">
              <li>
                <a href="/docs" class="text-sm text-base-content/70 hover:text-primary transition-colors">Help center</a>
              </li>
              <li>
                <a href="mailto:<EMAIL>" class="text-sm text-base-content/70 hover:text-primary transition-colors">Contact</a>
              </li>
              <li>
                <router-link to="/terms-of-service" class="text-sm text-base-content/70 hover:text-primary transition-colors">Terms</router-link>
              </li>
              <li>
                <router-link to="/privacy-policy" class="text-sm text-base-content/70 hover:text-primary transition-colors">Privacy</router-link>
              </li>
            </ul>
          </div>
        </div>
        <div class="mt-8 border-t border-base-300/50 pt-8">
          <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p class="text-sm text-base-content/70">
              © 2025 EmailConnect.eu. All rights reserved.
            </p>
            <div class="flex items-center space-x-4">
              <span class="text-xs text-base-content/50">Made with ❤️ in the EU</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<!-- Guest layout component - for public marketing/info pages -->

<style scoped>
/* Guest layout specific styles */
</style>
